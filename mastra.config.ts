import { MastraConfig } from "@mastra/core";

export default {
  name: "aicode-vscode-extension",
  agents: {
    dirPath: "./src/mastra/agents",
  },
  workflows: {
    dirPath: "./src/mastra/workflows",
  },
  tools: {
    dirPath: "./src/mastra/tools",
  },
  syncs: {
    dirPath: "./src/mastra/syncs",
  },
  db: {
    provider: "sqlite",
    uri: "file:./mastra.db",
  },
  logs: {
    type: "CONSOLE",
    level: "INFO",
  },
  telemetry: {
    instructorMode: "DEVELOPMENT",
  },
} satisfies MastraConfig;
