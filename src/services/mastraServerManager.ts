import * as vscode from 'vscode';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';

export class MastraServerManager {
    private mastraProcess: ChildProcess | null = null;
    private context: vscode.ExtensionContext;
    private port: number = 4111;
    private isProduction: boolean;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        // 判断是否为生产环境（打包后的插件）
        this.isProduction = context.extensionMode === vscode.ExtensionMode.Production;
    }

    async start(): Promise<void> {
        try {
            console.log('启动 Mastra 服务器...');
            
            if (this.isProduction) {
                await this.startProductionServer();
            } else {
                await this.startDevelopmentServer();
            }

            // 等待服务器启动
            await this.waitForServer();
            
            vscode.window.showInformationMessage(`Mastra 服务器已启动在端口 ${this.port}`);
            console.log(`Mastra 服务器已启动: http://localhost:${this.port}`);
        } catch (error) {
            console.error('启动 Mastra 服务器失败:', error);
            vscode.window.showErrorMessage(`启动 Mastra 服务器失败: ${error}`);
        }
    }

    private async startDevelopmentServer(): Promise<void> {
        const workspaceRoot = this.getWorkspaceRoot();
        
        console.log('启动开发模式 Mastra 服务器...');
        
        // 使用 pnpm mastra dev 启动开发服务器
        this.mastraProcess = spawn('pnpm', ['mastra', 'dev', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'development'
            }
        });

        this.setupProcessHandlers('开发');
    }

    private async startProductionServer(): Promise<void> {
        const workspaceRoot = this.getWorkspaceRoot();
        
        console.log('启动生产模式 Mastra 服务器...');
        
        // 首先构建项目
        await this.buildProject();
        
        // 然后启动生产服务器
        this.mastraProcess = spawn('pnpm', ['mastra', 'start', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'production'
            }
        });

        this.setupProcessHandlers('生产');
    }

    private async buildProject(): Promise<void> {
        return new Promise((resolve, reject) => {
            const workspaceRoot = this.getWorkspaceRoot();
            
            console.log('构建 Mastra 项目...');
            
            const buildProcess = spawn('pnpm', ['mastra', 'build'], {
                cwd: workspaceRoot,
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            buildProcess.on('close', (code) => {
                if (code === 0) {
                    console.log('Mastra 项目构建成功');
                    resolve();
                } else {
                    reject(new Error(`构建失败，退出码: ${code}`));
                }
            });

            buildProcess.on('error', (error) => {
                reject(error);
            });
        });
    }

    private setupProcessHandlers(mode: string): void {
        if (!this.mastraProcess) return;

        this.mastraProcess.stdout?.on('data', (data) => {
            console.log(`[Mastra ${mode}] ${data.toString()}`);
        });

        this.mastraProcess.stderr?.on('data', (data) => {
            console.error(`[Mastra ${mode} Error] ${data.toString()}`);
        });

        this.mastraProcess.on('close', (code) => {
            console.log(`Mastra ${mode}服务器进程退出，代码: ${code}`);
            this.mastraProcess = null;
        });

        this.mastraProcess.on('error', (error) => {
            console.error(`Mastra ${mode}服务器进程错误:`, error);
            vscode.window.showErrorMessage(`Mastra ${mode}服务器错误: ${error.message}`);
        });
    }

    private async waitForServer(): Promise<void> {
        const maxAttempts = 30;
        const delay = 1000;

        for (let i = 0; i < maxAttempts; i++) {
            try {
                // 这里可以添加健康检查逻辑
                // 例如发送 HTTP 请求到 http://localhost:4111/api
                await new Promise(resolve => setTimeout(resolve, delay));
                
                // 简单检查进程是否还在运行
                if (this.mastraProcess && !this.mastraProcess.killed) {
                    return;
                }
            } catch (error) {
                // 继续等待
            }
        }

        throw new Error('Mastra 服务器启动超时');
    }

    private getWorkspaceRoot(): string {
        // 获取插件所在的工作区根目录
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].uri.fsPath;
        }
        
        // 如果没有工作区，使用插件目录
        return path.dirname(this.context.extensionPath);
    }

    stop(): void {
        if (this.mastraProcess) {
            console.log('停止 Mastra 服务器...');
            this.mastraProcess.kill('SIGTERM');
            this.mastraProcess = null;
        }
    }

    getServerUrl(): string {
        return `http://localhost:${this.port}`;
    }

    getApiUrl(): string {
        return `${this.getServerUrl()}/api`;
    }

    isRunning(): boolean {
        return this.mastraProcess !== null && !this.mastraProcess.killed;
    }
}
