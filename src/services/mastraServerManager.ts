import * as vscode from 'vscode';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';

export class MastraServerManager {
    private mastraProcess: ChildProcess | null = null;
    private context: vscode.ExtensionContext;
    private port: number = 4111;
    private isProduction: boolean;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;

        // 从配置中获取设置
        const config = vscode.workspace.getConfiguration('aicode');
        this.port = config.get('mastraPort', 4111);

        // 判断是否为生产环境（打包后的插件或强制生产模式）
        const forceProductionMode = config.get('useProductionMode', false);
        this.isProduction = context.extensionMode === vscode.ExtensionMode.Production || forceProductionMode;
    }

    async start(): Promise<void> {
        try {
            console.log('启动 Mastra 服务器...',this.isProduction);
            
            if (this.isProduction) {
                await this.startProductionServer();
            } else {
                await this.startDevelopmentServer();
            }

            // 等待服务器启动
            await this.waitForServer();
            vscode.window.showInformationMessage(`Mastra 服务器已启动在端口 ${this.port}`);
            console.log(`Mastra 服务器已启动: http://localhost:${this.port}/api`);
        } catch (error) {
            console.error('启动 Mastra 服务器失败:', error);
            vscode.window.showErrorMessage(`启动 Mastra 服务器失败: ${error}`);
        }
    }

    private async startDevelopmentServer(): Promise<void> {
        const workspaceRoot = this.getWorkspaceRoot();

        console.log(`启动开发模式 Mastra 服务器... (工作目录: ${workspaceRoot})`);

        // 检查是否有 pnpm
        const packageManager = await this.detectPackageManager(workspaceRoot);
        console.log(`使用 ${packageManager}`);
        // 使用检测到的包管理器启动开发服务器
        this.mastraProcess = spawn(packageManager, ['npm','run', 'dev', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'development'
            }
        });

        this.setupProcessHandlers('开发');
    }

    private async startProductionServer(): Promise<void> {
        const workspaceRoot = this.getWorkspaceRoot();

        console.log(`启动生产模式 Mastra 服务器... (工作目录: ${workspaceRoot})`);

        // 检测包管理器
        const packageManager = await this.detectPackageManager(workspaceRoot);

        // 首先构建项目
        await this.buildProject(packageManager);

        // 然后启动生产服务器
        this.mastraProcess = spawn(packageManager, ['mastra', 'start', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'production'
            }
        });

        this.setupProcessHandlers('生产');
    }

    private async buildProject(packageManager: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const workspaceRoot = this.getWorkspaceRoot();

            console.log(`构建 Mastra 项目... (使用 ${packageManager})`);

            const buildProcess = spawn(packageManager, ['mastra', 'build'], {
                cwd: workspaceRoot,
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            buildProcess.stdout?.on('data', (data) => {
                console.log(`[Mastra Build] ${data.toString()}`);
            });

            buildProcess.stderr?.on('data', (data) => {
                console.error(`[Mastra Build Error] ${data.toString()}`);
            });

            buildProcess.on('close', (code) => {
                if (code === 0) {
                    console.log('Mastra 项目构建成功');
                    resolve();
                } else {
                    reject(new Error(`构建失败，退出码: ${code}`));
                }
            });

            buildProcess.on('error', (error) => {
                reject(error);
            });
        });
    }

    private async detectPackageManager(workspaceRoot: string): Promise<string> {
        const fs = require('fs');

        // 检查 pnpm-lock.yaml
        if (fs.existsSync(path.join(workspaceRoot, 'pnpm-lock.yaml'))) {
            return 'pnpm';
        }

        // 检查 yarn.lock
        if (fs.existsSync(path.join(workspaceRoot, 'yarn.lock'))) {
            return 'yarn';
        }

        // 检查 package-lock.json
        if (fs.existsSync(path.join(workspaceRoot, 'package-lock.json'))) {
            return 'npm';
        }

        // 默认使用 npm
        console.log('未检测到锁文件，使用默认的 npm');
        return 'npm';
    }

    private setupProcessHandlers(mode: string): void {
        if (!this.mastraProcess) return;

        this.mastraProcess.stdout?.on('data', (data) => {
            console.log(`[Mastra ${mode}] ${data.toString()}`);
        });

        this.mastraProcess.stderr?.on('data', (data) => {
            console.error(`[Mastra ${mode} Error] ${data.toString()}`);
        });

        this.mastraProcess.on('close', (code) => {
            console.log(`Mastra ${mode}服务器进程退出，代码: ${code}`);
            this.mastraProcess = null;
        });

        this.mastraProcess.on('error', (error) => {
            console.error(`Mastra ${mode}服务器进程错误:`, error);
            vscode.window.showErrorMessage(`Mastra ${mode}服务器错误: ${error.message}`);
        });
    }

    private async waitForServer(): Promise<void> {
        const maxAttempts = 30;
        const delay = 10000;

        for (let i = 0; i < maxAttempts; i++) {
            try {
                // 健康检查：尝试连接服务器
                const response = await fetch(`http://localhost:${this.port}`, {
                    method: 'GET',
                    signal: AbortSignal.timeout(3000)
                });

                if (response.ok) {
                    console.log(`Mastra 服务器健康检查通过 (尝试 ${i + 1}/${maxAttempts})`);
                    return;
                }
            } catch (error) {
                // 服务器还未准备好，继续等待
                console.log(`等待 Mastra 服务器启动... (尝试 ${i + 1}/${maxAttempts})`);
            }

            await new Promise(resolve => setTimeout(resolve, delay));
        }

        throw new Error('Mastra 服务器启动超时，请检查日志');
    }

    private getWorkspaceRoot(): string {
        // 获取插件所在的工作区根目录
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].uri.fsPath;
        }
        
        // 如果没有工作区，使用插件目录
        return path.dirname(this.context.extensionPath);
    }

    stop(): void {
        if (this.mastraProcess) {
            console.log('停止 Mastra 服务器...');
            this.mastraProcess.kill('SIGTERM');
            this.mastraProcess = null;
        }
    }

    getServerUrl(): string {
        return `http://localhost:${this.port}`;
    }

    getApiUrl(): string {
        return `${this.getServerUrl()}/api`;
    }

    isRunning(): boolean {
        return this.mastraProcess !== null && !this.mastraProcess.killed;
    }
}
