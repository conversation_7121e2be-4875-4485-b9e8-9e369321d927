import * as vscode from 'vscode';
import { MastraServerManager } from '../services/mastraServerManager';
import { mastraClient } from '../services/mastraClient';

/**
 * 测试 Mastra 集成功能
 */
export async function testMastraIntegration(context: vscode.ExtensionContext): Promise<void> {
    console.log('开始测试 Mastra 集成...');

    try {
        // 1. 测试服务器管理器
        const serverManager = new MastraServerManager(context);
        
        console.log('1. 启动 Mastra 服务器...');
        await serverManager.start();
        
        // 等待服务器完全启动
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 2. 测试服务器状态
        console.log('2. 检查服务器状态...');
        const isRunning = serverManager.isRunning();
        console.log(`服务器运行状态: ${isRunning}`);
        
        if (!isRunning) {
            throw new Error('Mastra 服务器未正常启动');
        }
        
        // 3. 测试客户端连接
        console.log('3. 测试客户端连接...');
        const isHealthy = await mastraClient.healthCheck();
        console.log(`健康检查结果: ${isHealthy}`);
        
        if (!isHealthy) {
            throw new Error('Mastra 服务器健康检查失败');
        }
        
        // 4. 测试获取 agents
        console.log('4. 获取可用的 agents...');
        const agents = await mastraClient.getAgents();
        console.log(`找到 ${agents.length} 个 agents:`, agents.map(a => a.name));
        
        // 5. 测试 weather agent（如果存在）
        const weatherAgent = agents.find(a => a.id === 'weatherAgent');
        if (weatherAgent) {
            console.log('5. 测试 weather agent...');
            const response = await mastraClient.generateWithAgent('weatherAgent', {
                messages: [{ role: 'user', content: '你好，请介绍一下你自己' }]
            });
            console.log('Agent 响应:', response.content.substring(0, 100) + '...');
        } else {
            console.log('5. 未找到 weatherAgent，跳过测试');
        }
        
        // 6. 停止服务器
        console.log('6. 停止 Mastra 服务器...');
        serverManager.stop();
        
        console.log('✅ Mastra 集成测试完成！');
        vscode.window.showInformationMessage('Mastra 集成测试通过！');
        
    } catch (error) {
        console.error('❌ Mastra 集成测试失败:', error);
        vscode.window.showErrorMessage(`Mastra 集成测试失败: ${error}`);
        throw error;
    }
}

/**
 * 注册测试命令
 */
export function registerMastraTestCommand(context: vscode.ExtensionContext): vscode.Disposable {
    return vscode.commands.registerCommand('aicode.testMastraIntegration', async () => {
        try {
            await testMastraIntegration(context);
        } catch (error) {
            console.error('测试命令执行失败:', error);
        }
    });
}
